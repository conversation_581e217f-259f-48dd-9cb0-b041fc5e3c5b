import React from 'react';
import { User, UserRole } from '../../types/User';
import { apiService } from '../../services/apiService';
import { mockUsers } from '../../store/mockData/users';
import UserPicker from './UserPicker';

interface StudentSplitPickerProps {
  control: any;
  name: string;
  error?: string;
}

export default function StudentSplitPicker({
  control,
  name,
  error,
}: StudentSplitPickerProps) {

  // Fetch students for split selection using the new API
  const fetchStudents = async (): Promise<User[]> => {
    try {
      const fetchedStudents = await apiService.getUsers(true); // only_student=true
      return fetchedStudents;
    } catch (error) {
      console.error('Failed to fetch students:', error);
      // Fallback to mock data if API fails
      const fallbackStudents = mockUsers.filter(user => user.role === UserRole.STUDENT);
      return fallbackStudents;
    }
  };

  return (
    <UserPicker
      control={control}
      name={name}
      label="Split with Other users (Optional - Equal split only)"
      placeholder="Select students to split with"
      error={error}
      multiplePick={true}
      fetchUsers={fetchStudents}
      required={false}
    />
  );
}
