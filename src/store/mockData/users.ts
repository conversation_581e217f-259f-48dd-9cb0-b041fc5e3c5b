import { User, UserRole } from '../../types/User';

export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: 'Student',
    school_class: 'Class A',
    // Legacy fields for backward compatibility
    name: '<PERSON>',
    role: UserRole.STUDENT
  },
  {
    id: '2',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: 'Advisor',
    school_class: null,
    // Legacy fields for backward compatibility
    name: '<PERSON>',
    role: UserRole.STAFF,
    isAdvisor: true,
    assignedStudents: ['1', '3']
  },
  {
    id: '3',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: 'Student',
    school_class: 'Class B',
    // Legacy fields for backward compatibility
    name: '<PERSON>',
    role: UserRole.STUDENT
  },
  {
    id: '4',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    school_class: null,
    // Legacy fields for backward compatibility
    name: '<PERSON>',
    role: UserRole.STAFF,
    isAdvisor: false
  },
  {
    id: '5',
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    school_class: null,
    // Legacy fields for backward compatibility
    name: 'Admin User',
    role: UserRole.ADMIN
  },
  {
    id: '6',
    email: '<EMAIL>',
    first_name: 'Emma',
    last_name: 'Student',
    school_class: 'Class A',
    // Legacy fields for backward compatibility
    name: 'Emma Student',
    role: UserRole.STUDENT
  },
  {
    id: '7',
    email: '<EMAIL>',
    first_name: 'Alex',
    last_name: 'Student',
    school_class: 'Class C',
    // Legacy fields for backward compatibility
    name: 'Alex Student',
    role: UserRole.STUDENT
  },
  {
    id: '8',
    email: '<EMAIL>',
    first_name: 'Sophia',
    last_name: 'Student',
    school_class: 'Class B',
    // Legacy fields for backward compatibility
    name: 'Sophia Student',
    role: UserRole.STUDENT
  }
];

export const getCurrentUser = (): User => mockUsers[0]; // Default to student

export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find(user => user.email === email);
};

// Map Google emails to our user system
export const mapGoogleUserToAppUser = (googleEmail: string): User | null => {
  const user = getUserByEmail(googleEmail);
  if (user) {
    return user;
  }

  // If user not found, check if it's a valid TGS email domain
  if (googleEmail.endsWith('@thinkglobalschool.com')) {
    // For demo purposes, create a default student user for unknown TGS emails
    return {
      id: `temp_${Date.now()}`,
      email: googleEmail,
      name: googleEmail.split('@')[0].replace('.', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      role: UserRole.STUDENT,
    };
  }

  return null; // Not a TGS email
};
